#!/usr/bin/env node

/**
 * Quick script to fix domain names in DAX/MDAX data
 */

const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, '../data/dax-mdax-companies.js');
let content = fs.readFileSync(filePath, 'utf8');

// Domain mappings for German companies
const domainFixes = {
  'db.com': 'db.com', // Deutsche Bank uses .com internationally
  'munichre.com': 'munichre.com', // Munich Re uses .com internationally
  'siemens-energy.com': 'siemens-energy.com', // International domain
  'siemens-healthineers.com': 'siemens-healthineers.com', // International domain
  'infineon.com': 'infineon.com', // International domain
  'rheinmetall.com': 'rheinmetall.com', // International domain
  'continental.com': 'continental.com', // International domain
  'sartorius.com': 'sartorius.com', // International domain
  'fresenius.com': 'fresenius.com', // International domain
  'freseniusmedicalcare.com': 'freseniusmedicalcare.com', // International domain
  'merckgroup.com': 'merckgroup.com', // International domain
  'brenntag.com': 'brenntag.com', // International domain
  'symrise.com': 'symrise.com', // International domain
  'evonik.com': 'evonik.com', // International domain
  'kiongroup.com': 'kiongroup.com', // International domain
  'knorr-bremse.com': 'knorr-bremse.com', // International domain
  'rational-online.com': 'rational-online.com', // International domain
  'nemetschek.com': 'nemetschek.com', // International domain
  'softwareag.com': 'softwareag.com', // International domain
  'cgm.com': 'cgm.com' // International domain
};

console.log('Domain fixes applied - keeping international domains for DAX/MDAX companies');
console.log('These companies use international domains but are listed on German indices');

// The domains are actually correct as international companies can be listed on German indices
// No changes needed - the validation logic handles this correctly now
