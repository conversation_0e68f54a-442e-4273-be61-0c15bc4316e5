#!/usr/bin/env node

/**
 * Test script for German companies import functionality
 */

const { importCompanies, validateCompanyData, GERMAN_COMPANIES_DATA } = require('./import-german-companies');

async function runTests() {
  console.log('🧪 Running German Companies Import Tests...\n');

  // Test 1: Validation function
  console.log('Test 1: Data Validation');
  
  const validCompany = {
    name: 'Test Company GmbH',
    location: 'Berlin, Germany',
    size: 'medium',
    industry: 'Technology',
    domain: 'test.de'
  };
  
  const invalidCompany = {
    name: '',
    location: 'New York, USA',
    size: 'huge',
    industry: '',
    domain: 'test.com'
  };

  const validErrors = validateCompanyData(validCompany);
  const invalidErrors = validateCompanyData(invalidCompany);

  console.log(`✅ Valid company errors: ${validErrors.length} (expected: 0)`);
  console.log(`✅ Invalid company errors: ${invalidErrors.length} (expected: 5)`);
  console.log(`   Errors: ${invalidErrors.join(', ')}\n`);

  // Test 2: German companies data structure
  console.log('Test 2: German Companies Data');
  console.log(`✅ Total companies in dataset: ${GERMAN_COMPANIES_DATA.length}`);
  
  const germanDomains = GERMAN_COMPANIES_DATA.filter(c => c.domain && c.domain.endsWith('.de'));
  console.log(`✅ Companies with .de domains: ${germanDomains.length}`);
  
  const germanLocations = GERMAN_COMPANIES_DATA.filter(c => 
    c.location.toLowerCase().includes('germany') || 
    c.location.toLowerCase().includes('berlin') ||
    c.location.toLowerCase().includes('munich')
  );
  console.log(`✅ Companies with German locations: ${germanLocations.length}`);

  // Test 3: Company sizes
  const sizeDistribution = GERMAN_COMPANIES_DATA.reduce((acc, company) => {
    acc[company.size] = (acc[company.size] || 0) + 1;
    return acc;
  }, {});
  
  console.log('✅ Size distribution:', sizeDistribution);

  // Test 4: Industries
  const industries = [...new Set(GERMAN_COMPANIES_DATA.map(c => c.industry))];
  console.log(`✅ Unique industries: ${industries.length}`);
  console.log(`   Industries: ${industries.join(', ')}\n`);

  // Test 5: Required fields
  console.log('Test 3: Required Fields Check');
  let missingFields = 0;
  
  GERMAN_COMPANIES_DATA.forEach((company, index) => {
    const errors = validateCompanyData(company);
    if (errors.length > 0) {
      console.log(`❌ Company ${index + 1} (${company.name}): ${errors.join(', ')}`);
      missingFields++;
    }
  });
  
  if (missingFields === 0) {
    console.log('✅ All companies have valid required fields');
  } else {
    console.log(`❌ ${missingFields} companies have missing/invalid fields`);
  }

  console.log('\n🎉 Tests completed!');
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests };
