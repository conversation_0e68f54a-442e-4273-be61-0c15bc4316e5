'use client'

import { useState, useEffect } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { Search, MapPin, Building2, Users, Filter, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { CompanyCard } from '@/components/company-card'
import { SearchableMultiSelect } from '@/components/ui/searchable-multi-select'
// Removed benefit-utils import - using simple comma-separated approach for both filters
import type { Company, CompanyBenefit } from '@/types/database'

interface CompanyWithBenefits extends Company {
  company_benefits?: CompanyBenefit[]
}

export function CompanySearch() {
  const [companies, setCompanies] = useState<CompanyWithBenefits[]>([])
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setFilters] = useState({
    location: '',
    size: '',
    industry: '',
    benefits: '',
  })
  const [activeBenefitFilters, setActiveBenefitFilters] = useState<string[]>([])
  const [selectedIndustries, setSelectedIndustries] = useState<string[]>([])
  const [benefitOptions, setBenefitOptions] = useState<Array<{value: string, label: string, count: number}>>([])
  const [industryOptions, setIndustryOptions] = useState<Array<{value: string, label: string, count: number}>>([])
  const [loadingOptions, setLoadingOptions] = useState(false)
  const [urlParsed, setUrlParsed] = useState(false)

  const searchParams = useSearchParams()
  const router = useRouter()

  const fetchCompanies = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams()

      if (searchQuery) params.append('search', searchQuery)
      if (filters.location) params.append('location', filters.location)
      if (filters.size) params.append('size', filters.size)

      // Use selected industries from multi-select
      if (selectedIndustries.length > 0) {
        params.append('industry', selectedIndustries.join(','))
      }

      // Use selected benefits from multi-select (consistent with industry approach)
      if (activeBenefitFilters.length > 0) {
        params.append('benefits', activeBenefitFilters.join(','))
      }

      const response = await fetch(`/api/companies?${params}`)
      if (response.ok) {
        const data = await response.json()
        setCompanies(data)
      }
    } catch (error) {
      console.error('Error fetching companies:', error)
    } finally {
      setLoading(false)
    }
  }

  // Fetch filter options
  const fetchFilterOptions = async () => {
    setLoadingOptions(true)
    try {
      const [benefitsResponse, industriesResponse] = await Promise.all([
        fetch('/api/filter-options/benefits'),
        fetch('/api/filter-options/industries')
      ])

      if (benefitsResponse.ok) {
        const benefits = await benefitsResponse.json()
        setBenefitOptions(benefits)
      }

      if (industriesResponse.ok) {
        const industries = await industriesResponse.json()
        setIndustryOptions(industries)
      }
    } catch (error) {
      console.error('Error fetching filter options:', error)
    } finally {
      setLoadingOptions(false)
    }
  }

  // Initialize filters from URL parameters
  useEffect(() => {
    const benefitsParam = searchParams.get('benefits')
    if (benefitsParam) {
      setActiveBenefitFilters(benefitsParam.split(',').filter(Boolean))
    } else {
      setActiveBenefitFilters([])
    }

    const industryParam = searchParams.get('industry')
    if (industryParam) {
      setSelectedIndustries(industryParam.split(',').filter(Boolean))
    } else {
      setSelectedIndustries([])
    }

    const searchParam = searchParams.get('search')
    if (searchParam) {
      setSearchQuery(searchParam)
    } else {
      setSearchQuery('')
    }

    fetchFilterOptions()
    setUrlParsed(true)
  }, [searchParams])

  useEffect(() => {
    if (urlParsed) {
      fetchCompanies()
    }
  }, [urlParsed, searchQuery, filters.location, filters.size, activeBenefitFilters, selectedIndustries])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    fetchCompanies()
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  // Removed removeBenefitFilter and clearAllBenefitFilters functions
  // Benefits are now managed only through the SearchableMultiSelect component
  // to maintain consistency with industry filter behavior

  const handleBenefitSelectionChange = (selectedBenefits: string[]) => {
    setActiveBenefitFilters(selectedBenefits)

    // Update URL
    const params = new URLSearchParams(searchParams.toString())
    if (selectedBenefits.length > 0) {
      params.set('benefits', selectedBenefits.join(','))
    } else {
      params.delete('benefits')
    }

    const newUrl = params.toString() ? `/?${params.toString()}` : '/'
    router.replace(newUrl)
  }

  const handleIndustrySelectionChange = (selectedIndustryList: string[]) => {
    setSelectedIndustries(selectedIndustryList)

    // Update URL
    const params = new URLSearchParams(searchParams.toString())
    if (selectedIndustryList.length > 0) {
      params.set('industry', selectedIndustryList.join(','))
    } else {
      params.delete('industry')
    }

    const newUrl = params.toString() ? `/?${params.toString()}` : '/'
    router.replace(newUrl)
  }

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* Search Form */}
      <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6">
        <form onSubmit={handleSearch} className="space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 sm:w-5 sm:h-5" />
            <input
              type="text"
              placeholder="Search for benefits like 'Wellsport' or 'sabbatical leave'"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-9 sm:pl-10 pr-4 py-2.5 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white text-sm sm:text-base"
            />
          </div>

          {/* Filters */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Location"
                value={filters.location}
                onChange={(e) => handleFilterChange('location', e.target.value)}
                className="w-full pl-9 sm:pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white text-sm sm:text-base"
              />
            </div>

            <div className="relative">
              <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <select
                value={filters.size}
                onChange={(e) => handleFilterChange('size', e.target.value)}
                className="w-full pl-9 sm:pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none text-gray-900 bg-white text-sm sm:text-base"
              >
                <option value="">Company Size</option>
                <option value="startup">Startup</option>
                <option value="small">Small</option>
                <option value="medium">Medium</option>
                <option value="large">Large</option>
                <option value="enterprise">Enterprise</option>
              </select>
            </div>

            <SearchableMultiSelect
              options={industryOptions}
              selectedValues={selectedIndustries}
              onSelectionChange={handleIndustrySelectionChange}
              placeholder="Select Industries"
              icon={<Building2 className="w-4 h-4" />}
              loading={loadingOptions}
              maxDisplayed={2}
            />

            <SearchableMultiSelect
              options={benefitOptions}
              selectedValues={activeBenefitFilters}
              onSelectionChange={handleBenefitSelectionChange}
              placeholder="Select Benefits"
              icon={<Filter className="w-4 h-4" />}
              loading={loadingOptions}
              maxDisplayed={2}
            />
          </div>

          <Button type="submit" className="w-full sm:w-auto" disabled={loading}>
            {loading ? 'Searching...' : 'Search'}
          </Button>
        </form>
      </div>

      {/* Removed Active Benefit Filters section to maintain consistency with industry filter behavior */}

      {/* Results */}
      <div className="space-y-4">
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600 mt-2 text-sm sm:text-base">Loading companies...</p>
          </div>
        ) : companies.length > 0 ? (
          <>
            <p className="text-gray-600 text-sm sm:text-base px-1">
              Found {companies.length} companies
            </p>
            <div className="grid grid-cols-1 gap-3 sm:gap-4">
              {companies.map((company) => (
                <CompanyCard key={company.id} company={company} />
              ))}
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-600 text-sm sm:text-base px-4">No companies found. Try adjusting your search criteria.</p>
          </div>
        )}
      </div>
    </div>
  )
}
