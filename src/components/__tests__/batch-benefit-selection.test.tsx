import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { BatchBenefitSelection } from '../batch-benefit-selection'

// Mock fetch
global.fetch = jest.fn()

const mockBenefits = [
  {
    id: '1',
    name: 'Health Insurance',
    category: 'health',
    icon: '🏥'
  },
  {
    id: '2',
    name: 'Dental Insurance',
    category: 'health',
    icon: '🦷'
  },
  {
    id: '3',
    name: 'Paid Time Off',
    category: 'time_off',
    icon: '🏖️'
  },
  {
    id: '4',
    name: '401k Matching',
    category: 'financial',
    icon: '💰'
  }
]

const defaultProps = {
  companyId: 'company-1',
  companyName: 'Test Company',
  isOpen: true,
  onClose: jest.fn(),
  onSuccess: jest.fn(),
  existingBenefitIds: []
}

describe('BatchBenefitSelection', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockBenefits)
    })
  })

  it('renders the selection step initially', async () => {
    render(<BatchBenefitSelection {...defaultProps} />)
    
    expect(screen.getByText('Select Benefits')).toBeInTheDocument()
    expect(screen.getByText('Choose multiple benefits to add to Test Company')).toBeInTheDocument()
    
    await waitFor(() => {
      expect(screen.getByText('Health Insurance')).toBeInTheDocument()
    })
  })

  it('filters benefits by search term', async () => {
    render(<BatchBenefitSelection {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Health Insurance')).toBeInTheDocument()
    })

    const searchInput = screen.getByPlaceholderText('Search benefits...')
    fireEvent.change(searchInput, { target: { value: 'health' } })

    expect(screen.getByText('Health Insurance')).toBeInTheDocument()
    expect(screen.getByText('Dental Insurance')).toBeInTheDocument()
    expect(screen.queryByText('Paid Time Off')).not.toBeInTheDocument()
  })

  it('filters benefits by category', async () => {
    render(<BatchBenefitSelection {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Health Insurance')).toBeInTheDocument()
    })

    const categorySelect = screen.getByDisplayValue('All Categories')
    fireEvent.change(categorySelect, { target: { value: 'financial' } })

    expect(screen.getByText('401k Matching')).toBeInTheDocument()
    expect(screen.queryByText('Health Insurance')).not.toBeInTheDocument()
  })

  it('allows selecting and deselecting benefits', async () => {
    render(<BatchBenefitSelection {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Health Insurance')).toBeInTheDocument()
    })

    const healthInsuranceCard = screen.getByText('Health Insurance').closest('div')
    fireEvent.click(healthInsuranceCard!)

    expect(screen.getByText('1 benefit selected')).toBeInTheDocument()

    // Click again to deselect
    fireEvent.click(healthInsuranceCard!)
    expect(screen.getByText('0 benefits selected')).toBeInTheDocument()
  })

  it('proceeds to review step when benefits are selected', async () => {
    render(<BatchBenefitSelection {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Health Insurance')).toBeInTheDocument()
    })

    // Select a benefit
    const healthInsuranceCard = screen.getByText('Health Insurance').closest('div')
    fireEvent.click(healthInsuranceCard!)

    // Click review button
    const reviewButton = screen.getByText('Review Selection')
    fireEvent.click(reviewButton)

    expect(screen.getByText('Review Selection')).toBeInTheDocument()
    expect(screen.getByText('Review and confirm 1 selected benefit')).toBeInTheDocument()
  })

  it('allows removing benefits from selection in review step', async () => {
    render(<BatchBenefitSelection {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Health Insurance')).toBeInTheDocument()
    })

    // Select benefits
    const healthInsuranceCard = screen.getByText('Health Insurance').closest('div')
    const dentalInsuranceCard = screen.getByText('Dental Insurance').closest('div')
    fireEvent.click(healthInsuranceCard!)
    fireEvent.click(dentalInsuranceCard!)

    // Go to review
    fireEvent.click(screen.getByText('Review Selection'))

    // Remove one benefit
    const removeButtons = screen.getAllByRole('button')
    const removeButton = removeButtons.find(btn => btn.querySelector('svg')) // X icon
    fireEvent.click(removeButton!)

    expect(screen.getByText('1 benefit selected')).toBeInTheDocument()
  })

  it('submits batch selection successfully', async () => {
    const mockOnSuccess = jest.fn()
    const mockOnClose = jest.fn()

    ;(fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockBenefits)
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ success: true, added: 1 })
      })

    render(
      <BatchBenefitSelection 
        {...defaultProps} 
        onSuccess={mockOnSuccess}
        onClose={mockOnClose}
      />
    )
    
    await waitFor(() => {
      expect(screen.getByText('Health Insurance')).toBeInTheDocument()
    })

    // Select a benefit
    const healthInsuranceCard = screen.getByText('Health Insurance').closest('div')
    fireEvent.click(healthInsuranceCard!)

    // Go to review
    fireEvent.click(screen.getByText('Review Selection'))

    // Submit
    fireEvent.click(screen.getByText('Add 1 Benefit'))

    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith(
        '/api/companies/company-1/benefits/bulk',
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            benefitIds: ['1'],
            action: 'add'
          })
        })
      )
    })

    await waitFor(() => {
      expect(mockOnSuccess).toHaveBeenCalled()
      expect(mockOnClose).toHaveBeenCalled()
    })
  })

  it('handles API errors gracefully', async () => {
    ;(fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockBenefits)
      })
      .mockResolvedValueOnce({
        ok: false,
        json: () => Promise.resolve({ error: 'Failed to add benefits' })
      })

    render(<BatchBenefitSelection {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Health Insurance')).toBeInTheDocument()
    })

    // Select and submit
    const healthInsuranceCard = screen.getByText('Health Insurance').closest('div')
    fireEvent.click(healthInsuranceCard!)
    fireEvent.click(screen.getByText('Review Selection'))
    fireEvent.click(screen.getByText('Add 1 Benefit'))

    await waitFor(() => {
      expect(screen.getByText('Failed to add benefits')).toBeInTheDocument()
    })
  })

  it('excludes existing benefits from selection', async () => {
    render(
      <BatchBenefitSelection 
        {...defaultProps} 
        existingBenefitIds={['1', '2']} // Health and Dental Insurance
      />
    )
    
    await waitFor(() => {
      expect(screen.getByText('Paid Time Off')).toBeInTheDocument()
    })

    expect(screen.queryByText('Health Insurance')).not.toBeInTheDocument()
    expect(screen.queryByText('Dental Insurance')).not.toBeInTheDocument()
  })

  it('handles select all and deselect all functionality', async () => {
    render(<BatchBenefitSelection {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Health Insurance')).toBeInTheDocument()
    })

    // Select all
    fireEvent.click(screen.getByText('Select All Visible'))
    expect(screen.getByText('4 benefits selected')).toBeInTheDocument()

    // Deselect all
    fireEvent.click(screen.getByText('Deselect All Visible'))
    expect(screen.getByText('0 benefits selected')).toBeInTheDocument()
  })

  it('does not render when isOpen is false', () => {
    render(<BatchBenefitSelection {...defaultProps} isOpen={false} />)
    
    expect(screen.queryByText('Select Benefits')).not.toBeInTheDocument()
  })

  it('prevents closing during submission', async () => {
    const mockOnClose = jest.fn()

    ;(fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockBenefits)
      })
      .mockImplementationOnce(() => new Promise(() => {})) // Never resolves

    render(
      <BatchBenefitSelection 
        {...defaultProps} 
        onClose={mockOnClose}
      />
    )
    
    await waitFor(() => {
      expect(screen.getByText('Health Insurance')).toBeInTheDocument()
    })

    // Select and submit
    const healthInsuranceCard = screen.getByText('Health Insurance').closest('div')
    fireEvent.click(healthInsuranceCard!)
    fireEvent.click(screen.getByText('Review Selection'))
    fireEvent.click(screen.getByText('Add 1 Benefit'))

    // Should show submitting state
    await waitFor(() => {
      expect(screen.getByText('Adding Benefits')).toBeInTheDocument()
    })

    // Close button should not be present during submission
    expect(screen.queryByRole('button', { name: /close/i })).not.toBeInTheDocument()
  })
})
