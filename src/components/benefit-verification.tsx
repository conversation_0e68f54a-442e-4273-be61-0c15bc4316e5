'use client'

import { useState, useEffect } from 'react'
import { CheckCircle, XCircle, MessageSquare } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface BenefitVerificationProps {
  companyBenefitId: string
  benefitName: string
  companyName: string
  onVerificationComplete?: () => void
  companyAuthStatus?: AuthorizationStatus | null
  hideAuthRestriction?: boolean
}

interface AuthorizationStatus {
  authorized: boolean
  message: string
  requiresAuth?: boolean
  companyName?: string
  requiredDomain?: string
  userDomain?: string
}

export function BenefitVerification({
  companyBenefitId,
  benefitName,
  companyName,
  onVerificationComplete,
  companyAuthStatus,
  hideAuthRestriction = false
}: BenefitVerificationProps) {
  const [isSignedIn, setIsSignedIn] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showCommentForm, setShowCommentForm] = useState(false)
  const [comment, setComment] = useState('')
  const [selectedStatus, setSelectedStatus] = useState<'confirmed' | 'disputed' | null>(null)
  const [verificationCounts, setVerificationCounts] = useState({ confirmed: 0, disputed: 0, total: 0 })
  const [isLoadingCounts, setIsLoadingCounts] = useState(true)
  const [authStatus, setAuthStatus] = useState<AuthorizationStatus | null>(null)
  const [isLoadingAuth, setIsLoadingAuth] = useState(true)

  useEffect(() => {
    checkAuthStatus()
    fetchVerificationCounts()

    // Use provided company auth status if available, otherwise check authorization
    if (companyAuthStatus !== undefined) {
      setAuthStatus(companyAuthStatus)
      setIsLoadingAuth(false)
    } else {
      checkAuthorization()
    }
  }, [companyBenefitId, companyAuthStatus])

  const checkAuthStatus = async () => {
    try {
      const response = await fetch('/api/auth/me')
      setIsSignedIn(response.ok)
    } catch (error) {
      setIsSignedIn(false)
    }
  }

  const fetchVerificationCounts = async () => {
    try {
      setIsLoadingCounts(true)
      const response = await fetch(`/api/benefit-verifications/${companyBenefitId}`)
      if (response.ok) {
        const counts = await response.json()
        setVerificationCounts(counts)
      }
    } catch (error) {
      console.error('Error fetching verification counts:', error)
    } finally {
      setIsLoadingCounts(false)
    }
  }

  const checkAuthorization = async () => {
    try {
      setIsLoadingAuth(true)
      const response = await fetch(`/api/benefit-verifications/${companyBenefitId}/authorization`)
      if (response.ok) {
        const authResult = await response.json()
        setAuthStatus(authResult)
      } else {
        setAuthStatus({
          authorized: false,
          message: 'Unable to check authorization'
        })
      }
    } catch (error) {
      console.error('Error checking authorization:', error)
      setAuthStatus({
        authorized: false,
        message: 'Authorization check failed'
      })
    } finally {
      setIsLoadingAuth(false)
    }
  }

  const handleVerification = async (status: 'confirmed' | 'disputed') => {
    if (!isSignedIn) {
      // Redirect to sign in or show modal
      window.location.href = '/sign-in'
      return
    }

    if (status === 'disputed') {
      setSelectedStatus(status)
      setShowCommentForm(true)
      return
    }

    await submitVerification(status, '')
  }

  const submitVerification = async (status: 'confirmed' | 'disputed', comment: string) => {
    setIsSubmitting(true)
    try {
      const response = await fetch('/api/benefit-verifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          companyBenefitId,
          status,
          comment,
        }),
      })

      if (response.ok) {
        setShowCommentForm(false)
        setComment('')
        setSelectedStatus(null)
        // Refresh verification counts
        await fetchVerificationCounts()
        onVerificationComplete?.()
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to submit verification')
      }
    } catch (error) {
      console.error('Error submitting verification:', error)
      alert('Failed to submit verification')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCommentSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (selectedStatus) {
      submitVerification(selectedStatus, comment)
    }
  }

  if (showCommentForm) {
    return (
      <div className="bg-white border rounded-lg p-4 space-y-4">
        <div className="flex items-center space-x-2">
          <XCircle className="w-5 h-5 text-red-600" />
          <span className="font-medium">Disputing: {benefitName}</span>
        </div>
        
        <form onSubmit={handleCommentSubmit} className="space-y-3">
          <div>
            <label htmlFor="comment" className="block text-sm font-medium text-gray-700 mb-1">
              Please explain why you&apos;re disputing this benefit:
            </label>
            <textarea
              id="comment"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
              rows={3}
              placeholder="e.g., This benefit is no longer offered, or the details are incorrect..."
              required
            />
          </div>
          
          <div className="flex space-x-2">
            <Button 
              type="submit" 
              size="sm" 
              disabled={isSubmitting || !comment.trim()}
              className="bg-red-600 hover:bg-red-700"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Dispute'}
            </Button>
            <Button 
              type="button" 
              variant="outline" 
              size="sm"
              onClick={() => {
                setShowCommentForm(false)
                setSelectedStatus(null)
                setComment('')
              }}
            >
              Cancel
            </Button>
          </div>
        </form>
      </div>
    )
  }

  // If user is not authorized, return null - counts will be shown in the benefit card
  if (!isLoadingAuth && authStatus && !authStatus.authorized) {
    return null
  }

  return (
    <div className="bg-gray-50 border rounded-lg p-4">
      <div className="flex items-center justify-between mb-3">
        <div>
          <h4 className="font-medium text-gray-900">Verify this benefit</h4>
          <p className="text-sm text-gray-600">
            Does {companyName} actually offer {benefitName}?
          </p>
        </div>
        <MessageSquare className="w-5 h-5 text-gray-400" />
      </div>

      {/* Authorization Status and Action Buttons */}
      {isLoadingAuth ? (
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
          <span>Checking authorization...</span>
        </div>
      ) : authStatus?.authorized ? (
        <div className="space-y-2">
          <div className="flex space-x-2">
            <Button
              size="sm"
              onClick={() => handleVerification('confirmed')}
              disabled={isSubmitting}
              className="flex items-center space-x-1 bg-green-600 hover:bg-green-700"
            >
              <CheckCircle className="w-4 h-4" />
              <span>Confirm</span>
            </Button>

            <Button
              size="sm"
              onClick={() => handleVerification('disputed')}
              disabled={isSubmitting}
              className="flex items-center space-x-1 bg-red-600 hover:bg-red-700 text-white"
            >
              <XCircle className="w-4 h-4" />
              <span>Dispute</span>
            </Button>
          </div>
          <p className="text-xs text-green-600">
            ✓ {authStatus.message}
          </p>
        </div>
      ) : null}
    </div>
  )
}
