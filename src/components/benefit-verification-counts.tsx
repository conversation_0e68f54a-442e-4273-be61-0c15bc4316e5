'use client'

import { useState, useEffect } from 'react'

interface BenefitVerificationCountsProps {
  companyBenefitId: string
  className?: string
}

interface VerificationCounts {
  confirmed: number
  disputed: number
  total: number
}

export function BenefitVerificationCounts({ 
  companyBenefitId, 
  className = '' 
}: BenefitVerificationCountsProps) {
  const [verificationCounts, setVerificationCounts] = useState<VerificationCounts>({ confirmed: 0, disputed: 0, total: 0 })
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchVerificationCounts()
  }, [companyBenefitId])

  const fetchVerificationCounts = async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/benefit-verifications/${companyBenefitId}`)
      if (response.ok) {
        const counts = await response.json()
        setVerificationCounts(counts)
      }
    } catch (error) {
      console.error('Error fetching verification counts:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Don't render anything if loading or no verifications
  if (isLoading || verificationCounts.total === 0) {
    return null
  }

  return (
    <div className={`text-xs text-gray-600 mt-2 ${className}`}>
      <div className="flex items-center space-x-1">
        <span className="text-green-600 font-medium">{verificationCounts.confirmed} confirmed</span>
        {verificationCounts.disputed > 0 && (
          <>
            <span>•</span>
            <span className="text-red-600 font-medium">{verificationCounts.disputed} disputed</span>
          </>
        )}
      </div>
      <div className="text-xs text-gray-500 mt-1">
        Benefits need 2+ confirmations and more confirmations than disputes to be verified
      </div>
    </div>
  )
}
