import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { getAnalyticsAccessInfo } from '@/lib/analytics-access-control'
import { generateDemoSearchTrends } from '@/lib/demo-analytics-generator'

// For demo purposes, we'll simulate search trends
// In production, you'd track actual search queries in a separate table

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '7d' // 7d, 30d, 90d
    const limit = parseInt(searchParams.get('limit') || '10')

    // Check analytics access level
    const accessInfo = await getAnalyticsAccessInfo()

    if (accessInfo.level === 'none') {
      return NextResponse.json(
        { error: 'Authentication required to access analytics' },
        { status: 401 }
      )
    }

    // If user is on demo mode, return demo data
    if (accessInfo.isDemoMode) {
      const demoData = generateDemoSearchTrends(period, limit)
      return NextResponse.json(demoData)
    }

    // Simulate search trends based on benefit popularity
    const trendsQuery = `
      SELECT 
        b.name as search_term,
        COUNT(cb.id) as search_count,
        b.category,
        b.icon,
        ROUND(COUNT(cb.id) * RANDOM() * 100) as trend_score
      FROM benefits b
      LEFT JOIN company_benefits cb ON b.id = cb.benefit_id
      GROUP BY b.id, b.name, b.category, b.icon
      ORDER BY search_count DESC, trend_score DESC
      LIMIT $1
    `

    const result = await query(trendsQuery, [limit])

    // Add simulated time-based data
    const trends = result.rows.map((row, index) => ({
      ...row,
      rank: index + 1,
      change: Math.floor(Math.random() * 20) - 10, // -10 to +10
      period_searches: Math.floor(row.search_count * (Math.random() * 0.5 + 0.5)),
      previous_period_searches: Math.floor(row.search_count * (Math.random() * 0.5 + 0.3))
    }))

    return NextResponse.json({
      period,
      trends,
      total_searches: trends.reduce((sum, trend) => sum + trend.period_searches, 0),
      generated_at: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error fetching search trends:', error)
    return NextResponse.json(
      { error: 'Failed to fetch search trends' },
      { status: 500 }
    )
  }
}
