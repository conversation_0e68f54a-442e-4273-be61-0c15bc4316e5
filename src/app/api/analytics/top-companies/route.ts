import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { getAnalyticsAccessInfo } from '@/lib/analytics-access-control'
import { generateDemoTopCompanies } from '@/lib/demo-analytics-generator'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '7d'
    const limit = parseInt(searchParams.get('limit') || '10')
    const benefitFilter = searchParams.get('benefit')

    // Check analytics access level
    const accessInfo = await getAnalyticsAccessInfo()

    if (accessInfo.level === 'none') {
      return NextResponse.json(
        { error: 'Authentication required to access analytics' },
        { status: 401 }
      )
    }

    // If user is on demo mode, return demo data
    if (accessInfo.isDemoMode) {
      const demoData = generateDemoTopCompanies(period, limit)
      return NextResponse.json(demoData)
    }

    let companiesQuery = `
      SELECT
        c.id,
        c.name,
        c.location,
        c.industry,
        COUNT(cb.id) as benefit_count,
        COUNT(CASE WHEN cb.is_verified = true THEN 1 END) as verified_benefit_count,
        ROUND(COUNT(cb.id) * RANDOM() * 1000) as view_count,
        ARRAY_AGG(
          CASE WHEN b.name IS NOT NULL
          THEN json_build_object('name', b.name, 'icon', b.icon, 'verified', cb.is_verified)
          END
        ) FILTER (WHERE b.name IS NOT NULL) as top_benefits
      FROM companies c
      LEFT JOIN company_benefits cb ON c.id = cb.company_id
      LEFT JOIN benefits b ON cb.benefit_id = b.id
    `

    const params: any[] = []
    let paramIndex = 1

    if (benefitFilter) {
      companiesQuery += ` WHERE b.name ILIKE $${paramIndex}`
      params.push(`%${benefitFilter}%`)
      paramIndex++
    }

    companiesQuery += `
      GROUP BY c.id, c.name, c.location, c.industry
      HAVING COUNT(cb.id) > 0
      ORDER BY view_count DESC, benefit_count DESC
      LIMIT $${paramIndex}
    `

    params.push(limit)

    const result = await query(companiesQuery, params)

    // Add simulated engagement metrics
    const companies = result.rows.map((row, index) => ({
      ...row,
      rank: index + 1,
      view_count: parseInt(row.view_count),
      engagement_rate: Math.round((Math.random() * 30 + 70) * 100) / 100, // 70-100%
      benefit_completion_rate: Math.round((row.verified_benefit_count / row.benefit_count) * 100),
      top_benefits: (row.top_benefits || []).slice(0, 3) // Top 3 benefits
    }))

    return NextResponse.json({
      period,
      benefit_filter: benefitFilter,
      companies,
      total_views: companies.reduce((sum, company) => sum + company.view_count, 0),
      generated_at: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error fetching top companies:', error)
    return NextResponse.json(
      { error: 'Failed to fetch top companies' },
      { status: 500 }
    )
  }
}
