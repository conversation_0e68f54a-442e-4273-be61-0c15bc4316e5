import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { canUserManageCompany } from '@/lib/auth'
import { getAnalyticsAccessInfo, canAccessCompanyAnalytics } from '@/lib/analytics-access-control'
import { generateDemoCompanyAnalytics } from '@/lib/demo-analytics-generator'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: companyId } = await params
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30d'

    // Check analytics access level
    const accessInfo = await getAnalyticsAccessInfo()

    if (accessInfo.level === 'none') {
      return NextResponse.json(
        { error: 'Authentication required to access analytics' },
        { status: 401 }
      )
    }

    // Check if user can access this company's analytics
    const canAccess = await canAccessCompanyAnalytics(companyId)

    if (!canAccess) {
      return NextResponse.json(
        { error: 'Unauthorized to access company analytics' },
        { status: 403 }
      )
    }

    // If user is on demo mode, return demo data
    if (accessInfo.isDemoMode) {
      const demoData = generateDemoCompanyAnalytics(companyId, period)
      return NextResponse.json(demoData)
    }

    // Get company basic info
    const companyResult = await query(
      'SELECT id, name, location, industry, verified FROM companies WHERE id = $1',
      [companyId]
    )

    if (companyResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    const company = companyResult.rows[0]

    // Get benefit analytics
    const benefitsResult = await query(`
      SELECT 
        b.name,
        b.category,
        b.icon,
        cb.is_verified,
        cb.created_at,
        ROUND(RANDOM() * 1000) as views,
        ROUND(RANDOM() * 100) as interactions
      FROM company_benefits cb
      JOIN benefits b ON cb.benefit_id = b.id
      WHERE cb.company_id = $1
      ORDER BY views DESC
    `, [companyId])

    // Generate simulated time-series data for views
    const generateTimeSeriesData = (days: number) => {
      const data = []
      const now = new Date()
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date(now)
        date.setDate(date.getDate() - i)
        data.push({
          date: date.toISOString().split('T')[0],
          views: Math.floor(Math.random() * 100) + 20,
          searches: Math.floor(Math.random() * 50) + 10
        })
      }
      return data
    }

    const timeSeriesData = generateTimeSeriesData(30)
    const totalViews = timeSeriesData.reduce((sum, day) => sum + day.views, 0)
    const totalSearches = timeSeriesData.reduce((sum, day) => sum + day.searches, 0)

    // Calculate benefit performance
    const benefitPerformance = benefitsResult.rows.map(benefit => ({
      ...benefit,
      performance_score: benefit.is_verified ? 
        Math.round((benefit.views * 0.7 + benefit.interactions * 0.3)) :
        Math.round((benefit.views * 0.4 + benefit.interactions * 0.2))
    }))

    // Top performing benefits
    const topBenefits = benefitPerformance
      .sort((a, b) => b.performance_score - a.performance_score)
      .slice(0, 5)

    // Category breakdown
    const categoryBreakdown = benefitsResult.rows.reduce((acc, benefit) => {
      const category = benefit.category
      if (!acc[category]) {
        acc[category] = { count: 0, views: 0, verified: 0 }
      }
      acc[category].count++
      acc[category].views += benefit.views
      if (benefit.is_verified) acc[category].verified++
      return acc
    }, {} as Record<string, any>)

    return NextResponse.json({
      company,
      period,
      overview: {
        total_views: totalViews,
        total_searches: totalSearches,
        total_benefits: benefitsResult.rows.length,
        verified_benefits: benefitsResult.rows.filter(b => b.is_verified).length,
        avg_daily_views: Math.round(totalViews / 30),
        engagement_rate: Math.round((Math.random() * 20 + 75) * 100) / 100 // 75-95%
      },
      time_series: timeSeriesData,
      top_benefits: topBenefits,
      category_breakdown: Object.entries(categoryBreakdown).map(([category, data]) => ({
        category,
        ...(data as object)
      })),
      benefit_performance: benefitPerformance,
      generated_at: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error fetching company analytics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch company analytics' },
      { status: 500 }
    )
  }
}
