import { NextRequest, NextResponse } from 'next/server'
import { requireAuth, getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { logBenefitVerified, logBenefitDisputed } from '@/lib/activity-logger'

export async function POST(request: NextRequest) {
  try {
    const userId = await requireAuth()
    const user = await getCurrentUser()

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { companyBenefitId, status, comment } = body

    if (!companyBenefitId || !status) {
      return NextResponse.json(
        { error: 'Company benefit ID and status are required' },
        { status: 400 }
      )
    }

    if (!['confirmed', 'disputed'].includes(status)) {
      return NextResponse.json(
        { error: 'Status must be either "confirmed" or "disputed"' },
        { status: 400 }
      )
    }

    // Check domain authorization
    const domainAuthResult = await checkDomainAuthorization(user.email, companyBenefitId)
    if (!domainAuthResult.authorized) {
      return NextResponse.json(
        { error: domainAuthResult.message },
        { status: 403 }
      )
    }

    // Check if user has already verified this benefit
    const existingVerification = await query(
      'SELECT id FROM benefit_verifications WHERE company_benefit_id = $1 AND user_id = $2',
      [companyBenefitId, userId]
    )

    if (existingVerification.rows.length > 0) {
      return NextResponse.json(
        { error: 'You have already verified this benefit' },
        { status: 400 }
      )
    }

    // Create new verification
    const result = await query(
      `INSERT INTO benefit_verifications (company_benefit_id, user_id, status, comment)
       VALUES ($1, $2, $3, $4)
       RETURNING *`,
      [companyBenefitId, userId, status, comment]
    )

    const verification = result.rows[0]

    // Get company and benefit details for activity logging
    const detailsResult = await query(
      `SELECT
        c.id as company_id, c.name as company_name,
        b.id as benefit_id, b.name as benefit_name
       FROM company_benefits cb
       JOIN companies c ON cb.company_id = c.id
       JOIN benefits b ON cb.benefit_id = b.id
       WHERE cb.id = $1`,
      [companyBenefitId]
    )

    if (detailsResult.rows.length > 0) {
      const details = detailsResult.rows[0]
      const userName = `${user.firstName || ''} ${user.lastName || ''}`.trim() || undefined

      // Log the benefit verification activity
      if (status === 'confirmed') {
        await logBenefitVerified(
          details.benefit_id,
          details.benefit_name,
          details.company_id,
          details.company_name,
          userId,
          user.email,
          userName
        )
      } else if (status === 'disputed') {
        await logBenefitDisputed(
          details.benefit_id,
          details.benefit_name,
          details.company_id,
          details.company_name,
          userId,
          user.email,
          userName,
          comment
        )
      }
    }

    // Update company benefit verification status based on verifications
    await updateCompanyBenefitStatus(companyBenefitId)

    return NextResponse.json(verification, { status: 201 })
  } catch (error) {
    console.error('Error creating benefit verification:', error)
    return NextResponse.json(
      { error: 'Failed to create benefit verification' },
      { status: 500 }
    )
  }
}

async function updateCompanyBenefitStatus(companyBenefitId: string) {
  try {
    // Get all verifications for this company benefit
    const verifications = await query(
      'SELECT status FROM benefit_verifications WHERE company_benefit_id = $1',
      [companyBenefitId]
    )

    if (!verifications.rows || verifications.rows.length === 0) {
      // No verifications yet - keep as unverified (pending)
      await query(
        'UPDATE company_benefits SET is_verified = false WHERE id = $1',
        [companyBenefitId]
      )
      return
    }

    const confirmedCount = verifications.rows.filter(v => v.status === 'confirmed').length
    const disputedCount = verifications.rows.filter(v => v.status === 'disputed').length

    // Verification Logic:
    // - Need at least 2 confirmations to be verified
    // - Must have more confirmations than disputes
    // - If equal or more disputes than confirmations, remains unverified
    const isVerified = confirmedCount >= 2 && confirmedCount > disputedCount

    await query(
      'UPDATE company_benefits SET is_verified = $1 WHERE id = $2',
      [isVerified, companyBenefitId]
    )

    console.log(`Updated benefit ${companyBenefitId}: ${confirmedCount} confirmed, ${disputedCount} disputed → ${isVerified ? 'VERIFIED' : 'PENDING'}`)
  } catch (error) {
    console.error('Error updating company benefit status:', error)
  }
}

async function checkDomainAuthorization(userEmail: string, companyBenefitId: string) {
  try {
    // Get company domain from company_benefit with JOIN
    const result = await query(
      `SELECT cb.company_id, c.domain, c.name
       FROM company_benefits cb
       JOIN companies c ON cb.company_id = c.id
       WHERE cb.id = $1`,
      [companyBenefitId]
    )

    if (result.rows.length === 0) {
      return { authorized: false, message: 'Company benefit not found' }
    }

    const company = result.rows[0]
    if (!company.domain) {
      return { authorized: false, message: 'Company domain not configured' }
    }

    // Extract domain from user email
    const userDomain = userEmail.split('@')[1]

    if (userDomain !== company.domain) {
      return {
        authorized: false,
        message: `Employee verification required for ${company.name}`
      }
    }

    return { authorized: true, message: 'Authorized' }
  } catch (error) {
    console.error('Error checking domain authorization:', error)
    return { authorized: false, message: 'Authorization check failed' }
  }
}
