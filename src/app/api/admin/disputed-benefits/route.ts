import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { query } from '@/lib/local-db'

// GET /api/admin/disputed-benefits - Get all disputed benefit verifications
export async function GET(request: NextRequest) {
  try {
    await requireAdmin()
    
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') // 'pending', 'resolved', 'all'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    
    const offset = (page - 1) * limit
    
    // Get disputed benefits with company and benefit information
    let sql = `
      SELECT
        bv.id as verification_id,
        bv.company_benefit_id,
        bv.user_id,
        bv.status,
        bv.comment,
        bv.created_at,
        cb.is_verified as benefit_is_verified,
        c.id as company_id,
        c.name as company_name,
        c.domain as company_domain,
        b.id as benefit_id,
        b.name as benefit_name,
        b.category as benefit_category,
        b.icon as benefit_icon,
        u.email as user_email,
        u.first_name as user_first_name,
        u.last_name as user_last_name,
        -- Get verification counts for this benefit
        (SELECT COUNT(*) FROM benefit_verifications bv2
         WHERE bv2.company_benefit_id = cb.id AND bv2.status = 'confirmed') as confirmed_count,
        (SELECT COUNT(*) FROM benefit_verifications bv2
         WHERE bv2.company_benefit_id = cb.id AND bv2.status = 'disputed') as disputed_count
      FROM benefit_verifications bv
      JOIN company_benefits cb ON bv.company_benefit_id = cb.id
      JOIN companies c ON cb.company_id = c.id
      JOIN benefits b ON cb.benefit_id = b.id
      LEFT JOIN users u ON bv.user_id::uuid = u.id
      WHERE bv.status = 'disputed'
    `
    
    const params = []
    let paramIndex = 1
    
    // Add status filter if specified
    if (status && status !== 'all') {
      if (status === 'pending') {
        sql += ` AND cb.is_verified = false`
      } else if (status === 'resolved') {
        sql += ` AND cb.is_verified = true`
      }
    }
    
    sql += ` ORDER BY bv.created_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`
    params.push(limit, offset)
    
    const result = await query(sql, params)
    
    // Get total count for pagination
    let countSql = `
      SELECT COUNT(DISTINCT bv.id) as total
      FROM benefit_verifications bv
      JOIN company_benefits cb ON bv.company_benefit_id = cb.id
      WHERE bv.status = 'disputed'
    `
    
    if (status && status !== 'all') {
      if (status === 'pending') {
        countSql += ` AND cb.is_verified = false`
      } else if (status === 'resolved') {
        countSql += ` AND cb.is_verified = true`
      }
    }
    
    const countResult = await query(countSql)
    const total = parseInt(countResult.rows[0].total)
    
    return NextResponse.json({
      disputes: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching disputed benefits:', error)
    return NextResponse.json(
      { error: 'Failed to fetch disputed benefits' },
      { status: 500 }
    )
  }
}

// POST /api/admin/disputed-benefits - Resolve a disputed benefit
export async function POST(request: NextRequest) {
  try {
    await requireAdmin()
    
    const body = await request.json()
    const { companyBenefitId, action, reason } = body
    
    if (!companyBenefitId || !action) {
      return NextResponse.json(
        { error: 'Company benefit ID and action are required' },
        { status: 400 }
      )
    }
    
    if (!['approve', 'reject', 'reset'].includes(action)) {
      return NextResponse.json(
        { error: 'Action must be approve, reject, or reset' },
        { status: 400 }
      )
    }
    
    // Get current benefit information
    const benefitResult = await query(
      `SELECT cb.*, c.name as company_name, b.name as benefit_name
       FROM company_benefits cb
       JOIN companies c ON cb.company_id = c.id
       JOIN benefits b ON cb.benefit_id = b.id
       WHERE cb.id = $1`,
      [companyBenefitId]
    )
    
    if (benefitResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Company benefit not found' },
        { status: 404 }
      )
    }
    
    const benefit = benefitResult.rows[0]
    
    switch (action) {
      case 'approve':
        // Mark benefit as verified regardless of dispute count
        await query(
          'UPDATE company_benefits SET is_verified = true WHERE id = $1',
          [companyBenefitId]
        )
        break
        
      case 'reject':
        // Mark benefit as not verified and optionally remove disputed verifications
        await query(
          'UPDATE company_benefits SET is_verified = false WHERE id = $1',
          [companyBenefitId]
        )
        break
        
      case 'reset':
        // Remove all verifications for this benefit and reset status
        await query(
          'DELETE FROM benefit_verifications WHERE company_benefit_id = $1',
          [companyBenefitId]
        )
        await query(
          'UPDATE company_benefits SET is_verified = false WHERE id = $1',
          [companyBenefitId]
        )
        break
    }
    
    return NextResponse.json({
      message: `Benefit ${action}d successfully`,
      benefit: benefit,
      action: action,
      reason: reason || null
    })
    
  } catch (error) {
    console.error('Error resolving disputed benefit:', error)
    return NextResponse.json(
      { error: 'Failed to resolve disputed benefit' },
      { status: 500 }
    )
  }
}
