import { NextRequest, NextResponse } from 'next/server'
import { bulkAddCompanyBenefits, getBenefits } from '@/lib/database'
import { requireAuth, canUserManageCompany } from '@/lib/auth'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const userId = await requireAuth()
    const { id: companyId } = await params
    
    const canManage = await canUserManageCompany(companyId)
    if (!canManage) {
      return NextResponse.json(
        { error: 'You can only manage benefits for companies that match your email domain' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { benefits, format } = body

    if (!benefits || !Array.isArray(benefits)) {
      return NextResponse.json(
        { error: 'Benefits array is required' },
        { status: 400 }
      )
    }

    // Get all available benefits to match names to IDs
    const allBenefits = await getBenefits()
    const benefitMap = new Map(allBenefits.map(b => [b.name.toLowerCase(), b.id]))

    let benefitIds: string[] = []
    let errors: string[] = []

    if (format === 'names') {
      // Import by benefit names
      for (const benefitName of benefits) {
        const benefitId = benefitMap.get(benefitName.toLowerCase())
        if (benefitId) {
          benefitIds.push(benefitId)
        } else {
          errors.push(`Benefit not found: ${benefitName}`)
        }
      }
    } else {
      // Import by benefit IDs (default)
      benefitIds = benefits.filter(id => typeof id === 'string')
      if (benefitIds.length !== benefits.length) {
        errors.push('Some benefit IDs are invalid')
      }
    }

    if (benefitIds.length === 0) {
      return NextResponse.json(
        { error: 'No valid benefits to import', errors },
        { status: 400 }
      )
    }

    const result = await bulkAddCompanyBenefits(companyId, benefitIds, userId)
    
    return NextResponse.json({
      success: true,
      imported: result.length,
      total_requested: benefits.length,
      errors: errors.length > 0 ? errors : undefined
    })

  } catch (error) {
    console.error('Error importing company benefits:', error)
    return NextResponse.json(
      { error: 'Failed to import company benefits' },
      { status: 500 }
    )
  }
}
