import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { handleEmailChangeCompanyAssociation, validateCompanyDomain } from '../email-change-company-association'

// Mock dependencies
jest.mock('../local-db')
jest.mock('../database')
jest.mock('../email')

const mockQuery = jest.fn()
const mockGetCompanyByDomain = jest.fn()
const mockSendEmail = jest.fn()

// Mock implementations
jest.mock('../local-db', () => ({
  query: mockQuery
}))

jest.mock('../database', () => ({
  getCompanyByDomain: mockGetCompanyByDomain
}))

jest.mock('../email', () => ({
  sendEmail: mockSendEmail
}))

describe('Email Change Company Association', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  afterEach(() => {
    jest.resetAllMocks()
  })

  describe('handleEmailChangeCompanyAssociation', () => {
    it('should return no_change when email domain is unchanged', async () => {
      const result = await handleEmailChangeCompanyAssociation(
        'user-123',
        '<EMAIL>',
        '<EMAIL>',
        'John'
      )

      expect(result.success).toBe(true)
      expect(result.action).toBe('no_change')
      expect(result.message).toContain('Email domain unchanged')
    })

    it('should return error for invalid email format', async () => {
      const result = await handleEmailChangeCompanyAssociation(
        'user-123',
        '<EMAIL>',
        'invalid-email',
        'John'
      )

      expect(result.success).toBe(false)
      expect(result.action).toBe('no_change')
      expect(result.error).toBe('Invalid email format')
    })

    it('should remove company association when switching to personal email', async () => {
      // Mock user with existing company
      mockQuery.mockResolvedValueOnce({
        rows: [{ company_id: 'company-123' }]
      })

      // Mock successful company removal
      mockQuery.mockResolvedValueOnce({
        rows: []
      })

      const result = await handleEmailChangeCompanyAssociation(
        'user-123',
        '<EMAIL>',
        '<EMAIL>',
        'John'
      )

      expect(result.success).toBe(true)
      expect(result.action).toBe('company_removed')
      expect(result.previousCompanyId).toBe('company-123')
      expect(result.newCompanyId).toBe(null)
      expect(result.message).toContain('personal email address')
    })

    it('should require verification when switching to different company domain', async () => {
      // Mock user with existing company
      mockQuery.mockResolvedValueOnce({
        rows: [{ company_id: 'company-123' }]
      })

      // Mock new company found
      mockGetCompanyByDomain.mockResolvedValueOnce({
        id: 'company-456',
        name: 'New Company',
        domain: 'newcompany.com'
      })

      // Mock rate limit check (allowed)
      mockQuery.mockResolvedValueOnce({
        rows: [{ email_count: '0' }]
      })

      // Mock token creation
      mockQuery.mockResolvedValueOnce({ rows: [] }) // Delete existing tokens
      mockQuery.mockResolvedValueOnce({ rows: [] }) // Create new token

      // Mock email sending
      mockSendEmail.mockResolvedValueOnce({ success: true })

      // Mock recording email sent
      mockQuery.mockResolvedValueOnce({ rows: [] })

      const result = await handleEmailChangeCompanyAssociation(
        'user-123',
        '<EMAIL>',
        '<EMAIL>',
        'John'
      )

      expect(result.success).toBe(true)
      expect(result.action).toBe('verification_required')
      expect(result.previousCompanyId).toBe('company-123')
      expect(result.newCompanyId).toBe('company-456')
      expect(result.companyName).toBe('New Company')
      expect(result.verificationToken).toBeDefined()
      expect(mockSendEmail).toHaveBeenCalled()
    })

    it('should handle rate limiting for verification emails', async () => {
      // Mock user with existing company
      mockQuery.mockResolvedValueOnce({
        rows: [{ company_id: 'company-123' }]
      })

      // Mock new company found
      mockGetCompanyByDomain.mockResolvedValueOnce({
        id: 'company-456',
        name: 'New Company',
        domain: 'newcompany.com'
      })

      // Mock rate limit exceeded
      mockQuery.mockResolvedValueOnce({
        rows: [{ email_count: '5' }] // Exceeds limit of 3
      })

      const result = await handleEmailChangeCompanyAssociation(
        'user-123',
        '<EMAIL>',
        '<EMAIL>',
        'John'
      )

      expect(result.success).toBe(false)
      expect(result.action).toBe('verification_required')
      expect(result.error).toBe('Rate limit exceeded')
      expect(result.message).toContain('Rate limit exceeded')
      expect(mockSendEmail).not.toHaveBeenCalled()
    })

    it('should remove company association when new domain has no matching company', async () => {
      // Mock user with existing company
      mockQuery.mockResolvedValueOnce({
        rows: [{ company_id: 'company-123' }]
      })

      // Mock no company found for new domain
      mockGetCompanyByDomain.mockResolvedValueOnce(null)

      // Mock successful company removal
      mockQuery.mockResolvedValueOnce({ rows: [] })

      const result = await handleEmailChangeCompanyAssociation(
        'user-123',
        '<EMAIL>',
        '<EMAIL>',
        'John'
      )

      expect(result.success).toBe(true)
      expect(result.action).toBe('company_removed')
      expect(result.previousCompanyId).toBe('company-123')
      expect(result.newCompanyId).toBe(null)
      expect(result.message).toContain('does not match any registered company')
    })
  })

  describe('validateCompanyDomain', () => {
    it('should reject personal email domains', async () => {
      const result = await validateCompanyDomain('gmail.com')
      
      expect(result.valid).toBe(false)
      expect(result.reason).toContain('Personal email domains')
    })

    it('should reject invalid domain formats', async () => {
      const result = await validateCompanyDomain('invalid')
      
      expect(result.valid).toBe(false)
      expect(result.reason).toContain('Invalid domain format')
    })

    it('should accept valid company domains', async () => {
      const result = await validateCompanyDomain('company.com')
      
      expect(result.valid).toBe(true)
      expect(result.reason).toBeUndefined()
    })
  })
})
