/**
 * Demo Analytics Data Generator
 * Generates realistic but clearly marked sample data for non-paying users
 */

export interface DemoCompanyAnalytics {
  company: {
    id: string
    name: string
    location: string
    industry: string
    verified: boolean
  }
  period: string
  overview: {
    total_views: number
    total_searches: number
    total_benefits: number
    verified_benefits: number
    avg_daily_views: number
    engagement_rate: number
  }
  time_series: Array<{
    date: string
    views: number
    searches: number
  }>
  top_benefits: Array<{
    name: string
    category: string
    icon?: string
    is_verified: boolean
    views: number
    interactions: number
    performance_score: number
  }>
  category_breakdown: Array<{
    category: string
    count: number
    percentage: number
  }>
  benefit_performance: Array<{
    name: string
    category: string
    icon?: string
    is_verified: boolean
    views: number
    interactions: number
    performance_score: number
  }>
  generated_at: string
  is_demo_data: boolean
  demo_notice: string
}

export interface DemoSearchTrends {
  trends: Array<{
    rank: number
    search_term: string
    category: string
    search_count: number
    trend_score: number
    change: number
    icon?: string
  }>
  period: string
  total_searches: number
  is_demo_data: boolean
  demo_notice: string
}

export interface DemoTopCompanies {
  companies: Array<{
    rank: number
    id: string
    name: string
    location: string
    industry: string
    verified: boolean
    view_count: number
    benefit_count: number
    verified_benefit_count: number
    engagement_rate: number
    benefit_completion_rate: number
    top_benefits: Array<{
      name: string
      icon?: string
      verified: boolean
    }>
  }>
  period: string
  total_companies: number
  is_demo_data: boolean
  demo_notice: string
}

// Sample benefit categories and names for demo data
const DEMO_BENEFITS = [
  { name: 'Health Insurance', category: 'health', icon: '🏥' },
  { name: 'Dental Coverage', category: 'health', icon: '🦷' },
  { name: 'Vision Insurance', category: 'health', icon: '👁️' },
  { name: 'Paid Time Off', category: 'time_off', icon: '🏖️' },
  { name: 'Sick Leave', category: 'time_off', icon: '🤒' },
  { name: '401k Matching', category: 'financial', icon: '💰' },
  { name: 'Stock Options', category: 'financial', icon: '📈' },
  { name: 'Professional Development', category: 'development', icon: '📚' },
  { name: 'Conference Budget', category: 'development', icon: '🎤' },
  { name: 'Gym Membership', category: 'wellness', icon: '💪' },
  { name: 'Mental Health Support', category: 'wellness', icon: '🧠' },
  { name: 'Remote Work', category: 'work_life', icon: '🏠' },
  { name: 'Flexible Hours', category: 'work_life', icon: '⏰' },
  { name: 'Parental Leave', category: 'time_off', icon: '👶' },
  { name: 'Life Insurance', category: 'financial', icon: '🛡️' }
]

const DEMO_COMPANIES = [
  { name: 'TechCorp Solutions', location: 'San Francisco, CA', industry: 'Technology' },
  { name: 'InnovateLabs', location: 'Austin, TX', industry: 'Software' },
  { name: 'DataDriven Inc', location: 'Seattle, WA', industry: 'Analytics' },
  { name: 'CloudFirst Systems', location: 'Denver, CO', industry: 'Cloud Services' },
  { name: 'NextGen Robotics', location: 'Boston, MA', industry: 'Robotics' },
  { name: 'GreenTech Energy', location: 'Portland, OR', industry: 'Clean Energy' },
  { name: 'FinanceFlow', location: 'New York, NY', industry: 'Financial Services' },
  { name: 'HealthTech Plus', location: 'Chicago, IL', industry: 'Healthcare' },
  { name: 'EduSmart Platform', location: 'Raleigh, NC', industry: 'Education' },
  { name: 'RetailRevolution', location: 'Los Angeles, CA', industry: 'E-commerce' }
]

const DEMO_NOTICE = "This is sample data for demonstration purposes. Upgrade to Premium to access real analytics data."

/**
 * Generate demo company analytics data
 */
export function generateDemoCompanyAnalytics(companyId: string, period: string = '30d'): DemoCompanyAnalytics {
  const company = {
    id: companyId,
    name: DEMO_COMPANIES[Math.floor(Math.random() * DEMO_COMPANIES.length)].name,
    location: DEMO_COMPANIES[Math.floor(Math.random() * DEMO_COMPANIES.length)].location,
    industry: DEMO_COMPANIES[Math.floor(Math.random() * DEMO_COMPANIES.length)].industry,
    verified: Math.random() > 0.3
  }

  // Generate time series data
  const days = period === '7d' ? 7 : period === '30d' ? 30 : 90
  const time_series = []
  const now = new Date()
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(now)
    date.setDate(date.getDate() - i)
    time_series.push({
      date: date.toISOString().split('T')[0],
      views: Math.floor(Math.random() * 150) + 50,
      searches: Math.floor(Math.random() * 75) + 25
    })
  }

  const totalViews = time_series.reduce((sum, day) => sum + day.views, 0)
  const totalSearches = time_series.reduce((sum, day) => sum + day.searches, 0)

  // Generate benefit performance data
  const benefitCount = Math.floor(Math.random() * 8) + 5 // 5-12 benefits
  const benefits = DEMO_BENEFITS
    .sort(() => Math.random() - 0.5)
    .slice(0, benefitCount)
    .map((benefit, index) => ({
      ...benefit,
      is_verified: Math.random() > 0.4,
      views: Math.floor(Math.random() * 500) + 100,
      interactions: Math.floor(Math.random() * 100) + 20,
      performance_score: Math.floor(Math.random() * 100) + 50
    }))

  const verifiedBenefits = benefits.filter(b => b.is_verified).length

  // Category breakdown
  const categoryBreakdown = DEMO_BENEFITS.reduce((acc, benefit) => {
    if (!acc[benefit.category]) {
      acc[benefit.category] = { count: 0, percentage: 0 }
    }
    acc[benefit.category].count++
    return acc
  }, {} as Record<string, { count: number; percentage: number }>)

  Object.keys(categoryBreakdown).forEach(category => {
    categoryBreakdown[category].percentage = Math.round(
      (categoryBreakdown[category].count / benefitCount) * 100
    )
  })

  return {
    company,
    period,
    overview: {
      total_views: totalViews,
      total_searches: totalSearches,
      total_benefits: benefitCount,
      verified_benefits: verifiedBenefits,
      avg_daily_views: Math.round(totalViews / days),
      engagement_rate: Math.round((Math.random() * 25 + 70) * 100) / 100
    },
    time_series,
    top_benefits: benefits
      .sort((a, b) => b.performance_score - a.performance_score)
      .slice(0, 5),
    category_breakdown: Object.entries(categoryBreakdown).map(([category, data]) => ({
      category,
      ...data
    })),
    benefit_performance: benefits,
    generated_at: new Date().toISOString(),
    is_demo_data: true,
    demo_notice: DEMO_NOTICE
  }
}

/**
 * Generate demo search trends data
 */
export function generateDemoSearchTrends(period: string = '7d', limit: number = 10): DemoSearchTrends {
  const trends = DEMO_BENEFITS
    .sort(() => Math.random() - 0.5)
    .slice(0, limit)
    .map((benefit, index) => ({
      rank: index + 1,
      search_term: benefit.name,
      category: benefit.category,
      search_count: Math.floor(Math.random() * 1000) + 100,
      trend_score: Math.floor(Math.random() * 100) + 50,
      change: Math.floor(Math.random() * 40) - 20, // -20 to +20
      icon: benefit.icon
    }))
    .sort((a, b) => b.search_count - a.search_count)

  const totalSearches = trends.reduce((sum, trend) => sum + trend.search_count, 0)

  return {
    trends,
    period,
    total_searches: totalSearches,
    is_demo_data: true,
    demo_notice: DEMO_NOTICE
  }
}

/**
 * Generate demo top companies data
 */
export function generateDemoTopCompanies(period: string = '7d', limit: number = 10): DemoTopCompanies {
  const companies = DEMO_COMPANIES
    .sort(() => Math.random() - 0.5)
    .slice(0, limit)
    .map((company, index) => {
      const benefitCount = Math.floor(Math.random() * 10) + 3
      const verifiedBenefitCount = Math.floor(benefitCount * (Math.random() * 0.5 + 0.5))
      const topBenefits = DEMO_BENEFITS
        .sort(() => Math.random() - 0.5)
        .slice(0, 3)
        .map(benefit => ({
          name: benefit.name,
          icon: benefit.icon,
          verified: Math.random() > 0.3
        }))

      return {
        rank: index + 1,
        id: `demo-company-${index + 1}`,
        name: company.name,
        location: company.location,
        industry: company.industry,
        verified: Math.random() > 0.4,
        view_count: Math.floor(Math.random() * 5000) + 1000,
        benefit_count: benefitCount,
        verified_benefit_count: verifiedBenefitCount,
        engagement_rate: Math.round((Math.random() * 30 + 70) * 100) / 100,
        benefit_completion_rate: Math.round((verifiedBenefitCount / benefitCount) * 100),
        top_benefits: topBenefits
      }
    })
    .sort((a, b) => b.view_count - a.view_count)

  return {
    companies,
    period,
    total_companies: companies.length,
    is_demo_data: true,
    demo_notice: DEMO_NOTICE
  }
}
